import * as fs from 'fs'
import * as path from 'path'
import { BaseTool } from './baseTool'

const toolsPath = path.join(__dirname, 'definitions')
const toolFiles = fs
  .readdirSync(toolsPath)
  .filter((file) => file.endsWith('.js') || file.endsWith('.ts'))

const loadedTools: BaseTool[] = []

for (const file of toolFiles) {
  const modulePath = path.join(toolsPath, file)
  const toolModule = require(modulePath)
  const ToolClass = toolModule.default
  if (ToolClass && typeof ToolClass === 'function') {
    loadedTools.push(new ToolClass())
  }
}

export const tools = loadedTools.map((tool) => tool.getDefinition())
export const toolImplementations = loadedTools.reduce(
  (acc, tool) => {
    acc[tool.name] = tool.execute.bind(tool)
    return acc
  },
  {} as Record<string, (args: any) => Promise<any>>,
)
